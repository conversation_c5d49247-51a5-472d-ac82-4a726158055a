
// 引入搜索工具
const searchUtils = require('../../../utils/searchUtils');
// 引入统计工具
const statsUtils = require('../../../utils/statsUtils');
// 引入内容安全检测工具
const contentSecurityUtils = require('../../../utils/contentSecurityUtils');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 表单数据
    formData: {
      title: '',               // 标题
      titleType: '',           // 标题类型：植物名称、工具等
      content: '',             // 内容
      price: '',               // 价格
      province: '',            // 省份
      city: '',                // 城市
      district: '',            // 区/县
      contact: '',             // 联系人
      phone: '',               // 联系电话
      quantity: '',            // 需求数量
      unit: '棵',              // 默认单位为"棵"
      meterDiameter: '',       // 米经
      groundDiameter: '',      // 地径
      height: '',              // 高度
      canopy: '',              // 冠幅
      cup: '',                 // 杯口
      branchPoint: '',         // 分支点
      quality: '中等',          // 求购质量，默认选中普通
      purchaseArea: '四川省成都市',    // 求购范围
      purchaseLocation: '',    // 用苗地点
      isUrgent: false,
      outDate: '3天',           // 求购期限，默认选中3天
      // 额外的规格参数
      main_vine_length: '',    // 主蔓长度
      plant_density: '',         // 密度(株/m²)
      trunkDiameter: '',         // 杆径
      thorax_diameter: '',       // 胸径
      branch_count: '',          // 分支数
      plant_age: '',             // 苗龄
    },
    // 单位选项
    unitOptions: ['株', '公斤', '棵', '斤', '平方米', '厘米', '袋', '捆','杯'],
    // 继续发布弹窗控制
    showContinueModal: false,


    // Radio选项 - 期限
    outDateRadioOptions: [
      { value: '1天', label: '1天' },
      { value: '3天', label: '3天' },
      { value: '5天', label: '5天' },
      { value: '7天', label: '7天' }
    ],
    // Radio选项 - 质量
    qualityRadioOptions: [
      { value: '精品', label: '精品' },
      { value: '中等', label: '中等' },
      { value: '一般', label: '一般' }
    ],
    // 图片列表
    imageList: [],
    // 是否正在提交
    submitting: false,
    // 表单验证错误
    errors: {},
    // 地区择器相关
    areaVisible: false,
    areaValue: [],
    areaText: '',
    provinces: [],
    cities: [],
    areaList: null,
    // 植物名推荐相关
    plantSuggestions: [],
    showSuggestions: false,
    searchTimer: null,
    titleInputFocused: false, // 标记植物名输入框是否获得焦点
    // 额外规格参数相关
    showExtraSpecs: false,  // 是否显示额外规格参数
    specAdded: {  // 记录哪些额外参数已添加
      mainVineLength: false,
      plantDensity: false,
      trunkDiameter: false,
      thoraxDiameter: false,
      branchCount: false,
      plantAge: false,
    },
    hasAddedSpecs: false  // 是否有已添加的额外参数
  },

  /**
   * 处理导航栏返回按钮点击事件
   */
  onNavBack() {
    const { formData, imageList } = this.data;
    
    // 检查是否上传了图片
    const hasImages = imageList.length > 0;
    
    // 检查主要表单字段
    const hasContent = formData.title || 
                      formData.content || 
                      formData.price || 
                      formData.height || 
                      formData.meterDiameter || 
                      formData.canopy || 
                      formData.groundDiameter || 
                      formData.contact || 
                      formData.phone;
    
    if (hasImages || hasContent) {
      wx.showModal({
        title: '确认返回',
        content: '返回后填写的内容将不会保存，确定要返回吗？',
        success: (res) => {
          if (res.confirm) {
            wx.navigateBack();
          }
        }
      });
    } else {
      // 如果表单没有内容，直接返回
      wx.navigateBack();
    }
  },

  /**
   * 切换到供应发布页面
   */
  switchToSupply: function() {
    // console.log('切换到供应页面');
    // 提示用户是否切换
    wx.showModal({
      title: '确认切换',
      content: '切换到供应发布页面，当前填写的内容将不会保存，确定要切换吗？',
      success: (res) => {
        if (res.confirm) {
          // 使用redirectTo替换当前页面，避免页面堆叠
          wx.redirectTo({
            url: '/pages/supply/publish/publish'
          });
        }
      }
    });
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 检查并获取openid
    this.checkAndGetOpenid();

    // 初始化页面数据
    this.initPage();

    // 初始化地区数据
    this.initAreaData();

    // 处理继续发布的参数
    if (options.phone) {
      this.setData({
        'formData.contactPhone': options.phone
      });
    }

    if (options.contact) {
      this.setData({
        'formData.contact': decodeURIComponent(options.contact)
      });
    }

    if (options.areaText && options.areaValue && options.purchaseArea) {
      try {
        const areaValue = JSON.parse(options.areaValue);
        this.setData({
          areaText: decodeURIComponent(options.areaText),
          areaValue: areaValue,
          'formData.purchaseArea': decodeURIComponent(options.purchaseArea)
        });
      } catch (e) {
        console.error('解析地区参数失败:', e);
        // 设置默认采购范围为四川省成都市
        this.setData({
          areaText: '四川省成都市',
          areaValue: ['510000', '510100'] // 四川省(510000)成都市(510100)的编码
        });
      }
    } else {
      // 设置默认采购范围为四川省成都市
      this.setData({
        areaText: '四川省成都市',
        areaValue: ['510000', '510100'] // 四川省(510000)成都市(510100)的编码
      });
    }
  },

  /**
   * 初始化页面
   */
  initPage() {
    // 获取App实例
    const app = getApp();
    
    // 获取用户信息
    let userInfo = app.globalData.userInfo;
    const userId = app.globalData.userId;
    
    // 如果全局状态没有，则从本地存储获取
    if (!userInfo) {
      userInfo = wx.getStorageSync('userInfo');
    }
    
    // 如果有用户信息，自动填充联系人
    if (userInfo) {
      this.setData({
        'formData.contact': userInfo.nickName || ''
      });
      
      // 如果用户信息中已有电话号码，直接使用
      if (userInfo.phoneNumber) {
        this.setData({
          'formData.contactPhone': userInfo.phoneNumber
        });
      }
    }
  },

  /**
   * 初始化地区数据
   */
  initAreaData() {
    // 中国省份数据（删除台湾、香港、澳门）
    const provinces = {
      '000': '全国',
      110000: '北京市',
      120000: '天津市',
      130000: '河北省',
      140000: '山西省',
      150000: '内蒙古自治区',
      210000: '辽宁省',
      220000: '吉林省',
      230000: '黑龙江省',
      310000: '上海市',
      320000: '江苏省',
      330000: '浙江省',
      340000: '安徽省',
      350000: '福建省',
      360000: '江西省',
      370000: '山东省',
      410000: '河南省',
      420000: '湖北省',
      430000: '湖南省',
      440000: '广东省',
      450000: '广西壮族自治区',
      460000: '海南省',
      500000: '重庆市',
      510000: '四川省',
      520000: '贵州省',
      530000: '云南省',
      540000: '西藏自治区',
      610000: '陕西省',
      620000: '甘肃省',
      630000: '青海省',
      640000: '宁夏回族自治区',
      650000: '新疆维吾尔自治区'
    };

    // 获取城市数据
    const cities = this.getCityData();

    // 初始化省份选项
    const provinceOptions = this.getOptions(provinces);
    this.setData({
      provinces: provinceOptions
    });

    // 初始化城市选项（默认选择四川省的城市）
    const sichuanProvinceCode = '510000';
    const cityOptions = this.getCities(sichuanProvinceCode, { cities });
    this.setData({
      cities: cityOptions
    });
  },

  /**
   * 获取选项数组
   */
  getOptions(obj) {
    const options = Object.keys(obj).map((key) => ({ value: key, label: obj[key] }));
    
    // 确保全国选项始终在第一位，四川省在第二位
    // 先找到并移除全国和四川省选项
    const allChinaIndex = options.findIndex(item => item.value === '000');
    const sichuanIndex = options.findIndex(item => item.value === '510000');
    
    let allChina = null;
    let sichuan = null;
    
    // 移除全国选项
    if (allChinaIndex > -1) {
      allChina = options.splice(allChinaIndex, 1)[0];
    }
    
    // 移除四川省选项
    if (sichuanIndex > -1) {
      sichuan = options.splice(sichuanIndex > allChinaIndex ? sichuanIndex - 1 : sichuanIndex, 1)[0];
    }
    
    // 将全国放在第一位，四川省放在第二位
    if (sichuan) {
      options.unshift(sichuan);
    }
    
    if (allChina) {
      options.unshift(allChina);
    }
    
    return options;
  },

  /**
   * 获取指定省份的城市列表
   */
  getCities(provinceCode, areaList) {
    // 如果是全国，返回空的城市列表
    if (provinceCode === '000') {
      return [];
    }
    
    const cityOptions = this.getOptions(areaList.cities);
    let filteredCities = [];
    
    // 处理直辖市的情况（北京、天津、上海、重庆）
    if (provinceCode === '110000' || provinceCode === '120000' || 
        provinceCode === '310000' || provinceCode === '500000') {
      // 直辖市返回区县数据
      filteredCities = cityOptions.filter(city => 
        city.value.toString().slice(0, 3) === provinceCode.toString().slice(0, 3)
      );
    } else {
      // 普通省份返回地级市数据
      filteredCities = cityOptions.filter(city => {
        // 匹配省份编码前两位
        const matchProvince = city.value.toString().slice(0, 2) === provinceCode.toString().slice(0, 2);
        // 地级市编码通常是以00结尾的6位数，但也有特殊情况如省直辖县级市
        const isCity = city.value.toString().length === 6;
        return matchProvince && isCity;
      });
    }
    
    // 在城市列表前添加"空"选项
    const emptyOption = { value: provinceCode + '_empty', label: '空' };
    return [emptyOption, ...filteredCities];
  },

  /**
   * 匹配前几位编码
   */
  match(value1, value2, size) {
    return value1.toString().slice(0, size) === value2.toString().slice(0, size);
  },

  /**
   * 打开地区选择器
   */
  onAreaPicker() {
    // 如果没有设置areaValue，默认选中四川省成都市
    if (!this.data.areaValue || this.data.areaValue.length === 0) {
      // 获取四川省的索引
      const sichuanIndex = this.data.provinces.findIndex(item => item.value === '510000');
      if (sichuanIndex !== -1) {
        // 获取城市数据
        const cityData = this.getCityData();
        // 获取四川省的城市列表
        const cities = this.getCities('510000', { cities: cityData });
        // 获取成都市的索引
        const chengduIndex = cities.findIndex(item => item.value === '510100');
        
        // 设置选中的值
        this.setData({
          cities: cities,
          areaValue: ['510000', '510100']
        });
      }
    }
    
    this.setData({
      areaVisible: true
    });
  },

  /**
   * 地区选择器取消事件
   */
  onPickerCancel() {
    this.setData({
      areaVisible: false
    });
  },

  /**
   * 地区选择器列变化事件
   */
  onColumnChange(e) {
    const { column, index } = e.detail;
    const { provinces } = this.data;

    if (column === 0) {
      // 更改省份时，更新城市列表
      const provinceCode = provinces[index].value;
      
      // 获取完整的城市数据
      const cityData = this.getCityData();
      
      // 获取当前选中省份的城市列表
      const cities = this.getCities(provinceCode, { cities: cityData });
      
      // 调试信息：打印当前选中的省份和对应的城市列表
      // console.log('省份变更', {
      //   province: provinces[index].label,
      //   provinceCode: provinceCode,
      //   citiesCount: cities.length,
      //   cities: cities.map(city => city.label).join(', ')
      // });
      
      // 更新城市列表并重置选中的城市
      this.setData({
        cities: cities,
        areaValue: [provinceCode, cities.length > 0 ? cities[0].value : '']
      });
    }
  },

  /**
   * 获取完整的城市数据
   */
  getCityData() {
    return {
      // 北京市辖区
      110101: '东城区',
      110102: '西城区',
      110105: '朝阳区',
      110106: '丰台区',
      110107: '石景山区',
      110108: '海淀区',
      110109: '门头沟区',
      110111: '房山区',
      110112: '通州区',
      110113: '顺义区',
      110114: '昌平区',
      110115: '大兴区',
      110116: '怀柔区',
      110117: '平谷区',
      110118: '密云区',
      110119: '延庆区',
      
      // 天津市辖区
      120101: '和平区',
      120102: '河东区',
      120103: '河西区',
      120104: '南开区',
      120105: '河北区',
      120106: '红桥区',
      120110: '东丽区',
      120111: '西青区',
      120112: '津南区',
      120113: '北辰区',
      120114: '武清区',
      120115: '宝坻区',
      120116: '滨海新区',
      120117: '宁河区',
      120118: '静海区',
      120119: '蓟州区',
      
      // 河北省
      130100: '石家庄市',
      130200: '唐山市',
      130300: '秦皇岛市',
      130400: '邯郸市',
      130500: '邢台市',
      130600: '保定市',
      130700: '张家口市',
      130800: '承德市',
      130900: '沧州市',
      131000: '廊坊市',
      131100: '衡水市',
      
      // 山西省
      140100: '太原市',
      140200: '大同市',
      140300: '阳泉市',
      140400: '长治市',
      140500: '晋城市',
      140600: '朔州市',
      140700: '晋中市',
      140800: '运城市',
      140900: '忻州市',
      141000: '临汾市',
      141100: '吕梁市',
      
      // 内蒙古自治区
      150100: '呼和浩特市',
      150200: '包头市',
      150300: '乌海市',
      150400: '赤峰市',
      150500: '通辽市',
      150600: '鄂尔多斯市',
      150700: '呼伦贝尔市',
      150800: '巴彦淖尔市',
      150900: '乌兰察布市',
      152200: '兴安盟',
      152500: '锡林郭勒盟',
      152900: '阿拉善盟',
      
      // 辽宁省
      210100: '沈阳市',
      210200: '大连市',
      210300: '鞍山市',
      210400: '抚顺市',
      210500: '本溪市',
      210600: '丹东市',
      210700: '锦州市',
      210800: '营口市',
      210900: '阜新市',
      211000: '辽阳市',
      211100: '盘锦市',
      211200: '铁岭市',
      211300: '朝阳市',
      211400: '葫芦岛市',
      
      // 吉林省
      220100: '长春市',
      220200: '吉林市',
      220300: '四平市',
      220400: '辽源市',
      220500: '通化市',
      220600: '白山市',
      220700: '松原市',
      220800: '白城市',
      222400: '延边朝鲜族自治州',
      
      // 黑龙江省
      230100: '哈尔滨市',
      230200: '齐齐哈尔市',
      230300: '鸡西市',
      230400: '鹤岗市',
      230500: '双鸭山市',
      230600: '大庆市',
      230700: '伊春市',
      230800: '佳木斯市',
      230900: '七台河市',
      231000: '牡丹江市',
      231100: '黑河市',
      231200: '绥化市',
      232700: '大兴安岭地区',
      
      // 上海市
      310101: '黄浦区',
      310104: '徐汇区',
      310105: '长宁区',
      310106: '静安区',
      310107: '普陀区',
      310109: '虹口区',
      310110: '杨浦区',
      310112: '闵行区',
      310113: '宝山区',
      310114: '嘉定区',
      310115: '浦东新区',
      310116: '金山区',
      310117: '松江区',
      310118: '青浦区',
      310120: '奉贤区',
      310151: '崇明区',
      
      // 江苏省
      320100: '南京市',
      320200: '无锡市',
      320300: '徐州市',
      320400: '常州市',
      320500: '苏州市',
      320600: '南通市',
      320700: '连云港市',
      320800: '淮安市',
      320900: '盐城市',
      321000: '扬州市',
      321100: '镇江市',
      321200: '泰州市',
      321300: '宿迁市',
      
      // 浙江省
      330100: '杭州市',
      330200: '宁波市',
      330300: '温州市',
      330400: '嘉兴市',
      330500: '湖州市',
      330600: '绍兴市',
      330700: '金华市',
      330800: '衢州市',
      330900: '舟山市',
      331000: '台州市',
      331100: '丽水市',
      
      // 安徽省
      340100: '合肥市',
      340200: '芜湖市',
      340300: '蚌埠市',
      340400: '淮南市',
      340500: '马鞍山市',
      340600: '淮北市',
      340700: '铜陵市',
      340800: '安庆市',
      341000: '黄山市',
      341100: '滁州市',
      341200: '阜阳市',
      341300: '宿州市',
      341500: '六安市',
      341600: '亳州市',
      341700: '池州市',
      341800: '宣城市',
      
      // 福建省
      350100: '福州市',
      350200: '厦门市',
      350300: '莆田市',
      350400: '三明市',
      350500: '泉州市',
      350600: '漳州市',
      350700: '南平市',
      350800: '龙岩市',
      350900: '宁德市',
      
      // 江西省
      360100: '南昌市',
      360200: '景德镇市',
      360300: '萍乡市',
      360400: '九江市',
      360500: '新余市',
      360600: '鹰潭市',
      360700: '赣州市',
      360800: '吉安市',
      360900: '宜春市',
      361000: '抚州市',
      361100: '上饶市',
      
      // 山东省
      370100: '济南市',
      370200: '青岛市',
      370300: '淄博市',
      370400: '枣庄市',
      370500: '东营市',
      370600: '烟台市',
      370700: '潍坊市',
      370800: '济宁市',
      370900: '泰安市',
      371000: '威海市',
      371100: '日照市',
      371300: '临沂市',
      371400: '德州市',
      371500: '聊城市',
      371600: '滨州市',
      371700: '菏泽市',
      
      // 河南省
      410100: '郑州市',
      410200: '开封市',
      410300: '洛阳市',
      410400: '平顶山市',
      410500: '安阳市',
      410600: '鹤壁市',
      410700: '新乡市',
      410800: '焦作市',
      410900: '濮阳市',
      411000: '许昌市',
      411100: '漯河市',
      411200: '三门峡市',
      411300: '南阳市',
      411400: '商丘市',
      411500: '信阳市',
      411600: '周口市',
      411700: '驻马店市',
      419001: '济源市',
      
      // 湖北省
      420100: '武汉市',
      420200: '黄石市',
      420300: '十堰市',
      420500: '宜昌市',
      420600: '襄阳市',
      420700: '鄂州市',
      420800: '荆门市',
      420900: '孝感市',
      421000: '荆州市',
      421100: '黄冈市',
      421200: '咸宁市',
      421300: '随州市',
      422800: '恩施土家族苗族自治州',
      429004: '仙桃市',
      429005: '潜江市',
      429006: '天门市',
      429021: '神农架林区',
      
      // 湖南省
      430100: '长沙市',
      430200: '株洲市',
      430300: '湘潭市',
      430400: '衡阳市',
      430500: '邵阳市',
      430600: '岳阳市',
      430700: '常德市',
      430800: '张家界市',
      430900: '益阳市',
      431000: '郴州市',
      431100: '永州市',
      431200: '怀化市',
      431300: '娄底市',
      433100: '湘西土家族苗族自治州',
      
      // 广东省
      440100: '广州市',
      440200: '韶关市',
      440300: '深圳市',
      440400: '珠海市',
      440500: '汕头市',
      440600: '佛山市',
      440700: '江门市',
      440800: '湛江市',
      440900: '茂名市',
      441200: '肇庆市',
      441300: '惠州市',
      441400: '梅州市',
      441500: '汕尾市',
      441600: '河源市',
      441700: '阳江市',
      441800: '清远市',
      441900: '东莞市',
      442000: '中山市',
      445100: '潮州市',
      445200: '揭阳市',
      445300: '云浮市',
      
      // 广西壮族自治区
      450100: '南宁市',
      450200: '柳州市',
      450300: '桂林市',
      450400: '梧州市',
      450500: '北海市',
      450600: '防城港市',
      450700: '钦州市',
      450800: '贵港市',
      450900: '玉林市',
      451000: '百色市',
      451100: '贺州市',
      451200: '河池市',
      451300: '来宾市',
      451400: '崇左市',
      
      // 海南省
      460100: '海口市',
      460200: '三亚市',
      460300: '三沙市',
      460400: '儋州市',
      469001: '五指山市',
      469002: '琼海市',
      469005: '文昌市',
      469006: '万宁市',
      469007: '东方市',
      469021: '定安县',
      469022: '屯昌县',
      469023: '澄迈县',
      469024: '临高县',
      469025: '白沙黎族自治县',
      469026: '昌江黎族自治县',
      469027: '乐东黎族自治县',
      469028: '陵水黎族自治县',
      469029: '保亭黎族苗族自治县',
      469030: '琼中黎族苗族自治县',
      
      // 重庆市辖区和县
      500101: '万州区',
      500102: '涪陵区',
      500103: '渝中区',
      500104: '大渡口区',
      500105: '江北区',
      500106: '沙坪坝区',
      500107: '九龙坡区',
      500108: '南岸区',
      500109: '北碚区',
      500110: '綦江区',
      500111: '大足区',
      500112: '渝北区',
      500113: '巴南区',
      500114: '黔江区',
      500115: '长寿区',
      500116: '江津区',
      500117: '合川区',
      500118: '永川区',
      500119: '南川区',
      500120: '璧山区',
      500151: '铜梁区',
      500152: '潼南区',
      500153: '荣昌区',
      500154: '开州区',
      500155: '梁平区',
      500156: '武隆区',
      500229: '城口县',
      500230: '丰都县',
      500231: '垫江县',
      500233: '忠县',
      500235: '云阳县',
      500236: '奉节县',
      500237: '巫山县',
      500238: '巫溪县',
      500240: '石柱土家族自治县',
      500241: '秀山土家族苗族自治县',
      500242: '酉阳土家族苗族自治县',
      500243: '彭水苗族土家族自治县',
      
      // 四川省
      510100: '成都市',
      510300: '自贡市',
      510400: '攀枝花市',
      510500: '泸州市',
      510600: '德阳市',
      510700: '绵阳市',
      510800: '广元市',
      510900: '遂宁市',
      511000: '内江市',
      511100: '乐山市',
      511300: '南充市',
      511400: '眉山市',
      511500: '宜宾市',
      511600: '广安市',
      511700: '达州市',
      511800: '雅安市',
      511900: '巴中市',
      512000: '资阳市',
      513200: '阿坝藏族羌族自治州',
      513300: '甘孜藏族自治州',
      513400: '凉山彝族自治州',
      
      // 贵州省
      520100: '贵阳市',
      520200: '六盘水市',
      520300: '遵义市',
      520400: '安顺市',
      520500: '毕节市',
      520600: '铜仁市',
      522300: '黔西南布依族苗族自治州',
      522600: '黔东南苗族侗族自治州',
      522700: '黔南布依族苗族自治州',
      
      // 云南省
      530100: '昆明市',
      530300: '曲靖市',
      530400: '玉溪市',
      530500: '保山市',
      530600: '昭通市',
      530700: '丽江市',
      530800: '普洱市',
      530900: '临沧市',
      532300: '楚雄彝族自治州',
      532500: '红河哈尼族彝族自治州',
      532600: '文山壮族苗族自治州',
      532800: '西双版纳傣族自治州',
      532900: '大理白族自治州',
      533100: '德宏傣族景颇族自治州',
      533300: '怒江傈僳族自治州',
      533400: '迪庆藏族自治州',
      
      // 西藏自治区
      540100: '拉萨市',
      540200: '日喀则市',
      540300: '昌都市',
      540400: '林芝市',
      540500: '山南市',
      540600: '那曲市',
      542500: '阿里地区',
      
      // 陕西省
      610100: '西安市',
      610200: '铜川市',
      610300: '宝鸡市',
      610400: '咸阳市',
      610500: '渭南市',
      610600: '延安市',
      610700: '汉中市',
      610800: '榆林市',
      610900: '安康市',
      611000: '商洛市',
      
      // 甘肃省
      620100: '兰州市',
      620200: '嘉峪关市',
      620300: '金昌市',
      620400: '白银市',
      620500: '天水市',
      620600: '武威市',
      620700: '张掖市',
      620800: '平凉市',
      620900: '酒泉市',
      621000: '庆阳市',
      621100: '定西市',
      621200: '陇南市',
      622900: '临夏回族自治州',
      623000: '甘南藏族自治州',
      
      // 青海省
      630100: '西宁市',
      630200: '海东市',
      632200: '海北藏族自治州',
      632300: '黄南藏族自治州',
      632500: '海南藏族自治州',
      632600: '果洛藏族自治州',
      632700: '玉树藏族自治州',
      632800: '海西蒙古族藏族自治州',
      
      // 宁夏回族自治区
      640100: '银川市',
      640200: '石嘴山市',
      640300: '吴忠市',
      640400: '固原市',
      640500: '中卫市',
      
      // 新疆维吾尔自治区
      650100: '乌鲁木齐市',
      650200: '克拉玛依市',
      650400: '吐鲁番市',
      650500: '哈密市',
      652300: '昌吉回族自治州',
      652700: '博尔塔拉蒙古自治州',
      652800: '巴音郭楞蒙古自治州',
      652900: '阿克苏地区',
      653000: '克孜勒苏柯尔克孜自治州',
      653100: '喀什地区',
      653200: '和田地区',
      654000: '伊犁哈萨克自治州',
      654200: '塔城地区',
      654300: '阿勒泰地区',
      659001: '石河子市',
      659002: '阿拉尔市',
      659003: '图木舒克市',
      659004: '五家渠市',
      659005: '北屯市',
      659006: '铁门关市',
      659007: '双河市',
      659008: '可克达拉市',
      659009: '昆玉市',
      659010: '胡杨河市'
    };
  },

  /**
   * 确认地区选择
   */
  onPickerChange(e) {
    const { value, label } = e.detail;
    
    if (value.length >= 1) {
      let areaText = '';
      let purchaseArea = '';
      
      // 处理全国选项
      if (value[0] === '000') {
        areaText = '全国';
        purchaseArea = '全国';
      } 
      // 处理省份+空选项
      else if (value.length >= 2 && value[1].includes('_empty')) {
        const provinceLabel = label[0] || '';
        areaText = provinceLabel;
        purchaseArea = provinceLabel;
      }
      // 处理省份+城市
      else if (value.length >= 2) {
        const provinceLabel = label[0] || '';
        const cityLabel = label[1] || '';
        areaText = provinceLabel + ' ' + cityLabel;
        purchaseArea = areaText;
      }
      
      this.setData({
        areaVisible: false,
        areaText: areaText,
        areaValue: value,
        'formData.purchaseArea': purchaseArea,
        'errors.purchaseArea': ''
      });
    }
  },

  /**
   * 检查并获取openid
   */
  checkAndGetOpenid: function() {
    const app = getApp();

    // 先检查用户是否已经登录
    const isLogined = app.globalData.isLogined || wx.getStorageSync('isLogined');
    const userInfo = app.globalData.userInfo || wx.getStorageSync('userInfo');

    if (!isLogined || !userInfo) {
      return;
    }

    const cachedOpenid = app.globalData.openid || wx.getStorageSync('openid');

    if (cachedOpenid) {
      return;
    }

    // 自动获取openid
    wx.cloud.callFunction({
      name: 'quickstartFunctions',
      data: { type: 'login' },
      success: res => {
        const openid = res.result.openid;
        if (openid) {
          // 保存到全局数据和本地存储
          app.globalData.openid = openid;
          wx.setStorageSync('openid', openid);
        } else {
          wx.showToast({
            title: '获取用户信息失败',
            icon: 'none',
            duration: 2000
          });
        }
      },
      fail: err => {
        wx.showToast({
          title: '网络异常，请检查网络连接',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  /**
   * 获取openid（带重试机制）
   */
  getOpenidWithRetry: function() {
    return new Promise((resolve) => {
      const app = getApp();
      const cachedOpenid = app.globalData.openid || wx.getStorageSync('openid');

      if (cachedOpenid) {
        resolve(cachedOpenid);
        return;
      }

      // 缓存中没有openid，尝试重新获取
      wx.cloud.callFunction({
        name: 'quickstartFunctions',
        data: { type: 'login' },
        success: res => {
          const openid = res.result.openid;
          if (openid) {
            // 保存到全局数据和本地存储
            app.globalData.openid = openid;
            wx.setStorageSync('openid', openid);

            resolve(openid);
          } else {
            resolve(null);
          }
        },
        fail: err => {
          resolve(null);
        }
      });
    });
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 确保tabbar正确显示
    if (typeof this.getTabBar === 'function') {
      const tabBar = this.getTabBar();
      if (tabBar) {
        tabBar.setData({
          active: 2  // 求购页面是第三个选项，索引为2
        });
      }
    }
  },

  /**
   * 处理输入框变化
   */
  onInputChange(e) {
    // 使用全局搜索工具处理输入变化
    if (e.currentTarget.dataset.field === 'title') {
      searchUtils.handleInputChange(this, e);
    } else {
      const { field } = e.currentTarget.dataset;
      const { value } = e.detail;
      
      this.setData({
        [`formData.${field}`]: value,
        [`errors.${field}`]: '' // 清除对应字段的错误
      });
    }
  },



  /**
   * 处理单位选择
   */
  onUnitChange(e) {
    this.setData({
      'formData.unit': this.data.unitOptions[e.detail.value],
      'errors.unit': ''
    });
  },

  /**
   * 选择图片
   */
  chooseImage() {
    const { imageList } = this.data;
    const remainCount = 3 - imageList.length;
    
    if (remainCount <= 0) {
      return;
    }
    
    wx.chooseImage({
      count: remainCount,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        this.setData({
          imageList: [...imageList, ...res.tempFilePaths],
          'errors.imageList': ''
        });
      }
    });
  },

  /**
   * 预览图片
   */
  previewImage(e) {
    const { index } = e.currentTarget.dataset;
    const { imageList } = this.data;
    
    wx.previewImage({
      current: imageList[index],
      urls: imageList
    });
  },

  /**
   * 删除图片
   */
  deleteImage(e) {
    const { index } = e.currentTarget.dataset;
    const { imageList } = this.data;
    
    imageList.splice(index, 1);
    this.setData({
      imageList
    });
  },

  /**
   * 价格类型切换
   */
  onPriceTypeChange(e) {
    const type = e.currentTarget.dataset.type;
    
    // 如果切换到"电话议价"，清空价格范围数据
    if (type === 'negotiate') {
      this.setData({
        'formData.price': '',
        'errors.price': ''
      });
    } else {
      this.setData({
        'formData.price': type
      });
    }
  },

  /**
   * 验证表单
   */
  validateForm() {
    const { formData, imageList } = this.data;
    const errors = {};
    
    // 植物名称验证
    if (!formData.title) {
      errors.title = '请输入植物名称';
    }
    
    // 数量验证
    if (!formData.quantity) {
      errors.quantity = '请输入需求数量';
    }
    
    // 质量验证 - 添加必选验证
    if (!formData.quality) {
      errors.quality = '请选择求购质量';
    }
    
    // 价格验证 - 改为非必填，只在填写时验证格式
    if (formData.price !== '' && formData.price !== null && formData.price !== undefined) {
      if (isNaN(parseFloat(formData.price)) || parseFloat(formData.price) < 0) {
        errors.price = '请输入有效的价格（不能为负数）';
      }
    }
    
    // 详细描述验证 - 移除必填验证，改为选填
   
    // 联系人验证
    if (!formData.contact) {
      errors.contact = '请输入联系人姓名';
    }
    
    // 联系电话验证
    if (!formData.contactPhone) {
      errors.contactPhone = '请输入联系电话';
    } else if (!/^1[3-9]\d{9}$/.test(formData.contactPhone)) {
      errors.contactPhone = '请输入正确的11位手机号码';
    }
    
    // 采购地点不是必填项，所以不进行验证
    // 图片也不是必填项，所以不进行验证
    
    this.setData({ errors });
    
    // 如果有错误，滚动到页面顶部
    if (Object.keys(errors).length > 0) {
      wx.pageScrollTo({
        scrollTop: 0,
        duration: 300
      });
    }
    
    return Object.keys(errors).length === 0;
  },

  /**
   * 提交表单
   */
  submitForm() {
    // 检查是否还在提交中
    if (this.data.submitting) {
      return;
    }

    // ✅ 立即设置提交状态，防止重复点击
    this.setData({ submitting: true });

    // 首先检查用户是否已登录
    const app = getApp();
    const isLogined = app.globalData.isLogined || wx.getStorageSync('isLogined');
    const userInfo = app.globalData.userInfo || wx.getStorageSync('userInfo');
    const userId = app.globalData.userId || wx.getStorageSync('userId');
    
    if (!isLogined || !userInfo || !userId) {
      // 用户未登录，重置状态并显示提示
      this.setData({ submitting: false });
      wx.showModal({
        title: '提示',
        content: '请先登录后再发布需求信息',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            // 跳转到用户中心页面
            wx.switchTab({
              url: '/pages/user/user'
            });
          }
        }
      });
      return;
    }
    
    // 然后进行表单验证
    if (this.validateForm()) {
      // 进行内容安全检测
      this.checkContentSecurity();
    } else {
      // 表单验证失败，重置提交状态
      this.setData({ submitting: false });
    }
  },

  /**
   * 检测内容安全
   */
  async checkContentSecurity() {
    // 显示检测提示
    wx.showLoading({
      title: '正在检测内容...',
      mask: true
    });

    try {
      // 获取用户openid，如果不存在则尝试重新获取
      const openid = await this.getOpenidWithRetry();

      if (!openid) {
        wx.hideLoading();
        this.setData({ submitting: false });
        wx.showToast({
          title: '获取用户信息失败，请重试',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      // 检测求购发布内容
      const checkResult = await contentSecurityUtils.checkSupplyPublishContent(
        this.data.formData,
        openid
      );

      wx.hideLoading();

      if (!checkResult.success) {
        // 检测服务异常
        // wx.showModal({
        //   title: '提示',
        //   content: '内容检测服务异常，是否继续发布？',
        //   success: (res) => {
        //     if (res.confirm) {
        //       this.showSubmitConfirm();
        //     }
        //   }
        // });
        // 暂时注释确认弹窗，直接发布
        this.doSubmit();
        return;
      }

      if (checkResult.hasRiskyContent) {
        // 发现敏感内容，阻止发布
        this.setData({ submitting: false });
        const errorMessage = this.generateSimpleErrorMessage(checkResult.riskyFields);
        wx.showModal({
          title: '内容审核未通过',
          content: errorMessage,
          showCancel: false,
          confirmText: '我知道了'
        });
        return;
      }

      // 内容安全，继续发布流程
      // this.showSubmitConfirm(); // 暂时注释确认弹窗
      this.doSubmit(); // 直接发布

    } catch (error) {
      wx.hideLoading();
      this.setData({ submitting: false });
      console.error('内容安全检测失败:', error);

      wx.showToast({
        title: '内容检测失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  },

  /**
   * 显示提交确认框
   */
  showSubmitConfirm() {
    wx.showModal({
      title: '确认发布',
      content: '您确定要发布此求购信息吗？',
      confirmText: '确认发布',
      confirmColor: '#07c160',
      success: (res) => {
        if (res.confirm) {
          // 用户点击确认后执行发布
          this.setData({ submitting: true });
          this.doSubmit();
        }
        // 用户点击取消则不执行任何操作
      }
    });
  },

  /**
   * 生成简化的错误提示消息
   */
  generateSimpleErrorMessage(riskyFields) {
    if (!riskyFields || riskyFields.length === 0) {
      return '输入内容包含不当信息，请检查并重新输入';
    }

    const fieldNames = [];
    riskyFields.forEach(item => {
      let fieldName = '';
      // 根据字段名映射到求购页面的字段名称
      switch(item.field) {
        case 'title':
          fieldName = '求购名';
          break;
        case 'quantity':
          fieldName = '数量';
          break;
        case 'content':
          fieldName = '补充信息';
          break;
        default:
          fieldName = contentSecurityUtils.getFieldDescription(item.field);
      }

      if (!fieldNames.includes(fieldName)) {
        fieldNames.push(fieldName);
      }
    });

    if (fieldNames.length === 1) {
      return `您输入的"${fieldNames[0]}"存在不当内容，请检查并重新输入`;
    } else if (fieldNames.length === 2) {
      return `您输入的"${fieldNames[0]}"或"${fieldNames[1]}"存在不当内容，请检查并重新输入`;
    } else {
      return `您输入的"${fieldNames.join('"、"')}"存在不当内容，请检查并重新输入`;
    }
  },

  /**
   * 执行提交
   */
  doSubmit() {
    wx.showLoading({
      title: '提交中...',
      mask: true
    });
    
    // 获取当前用户ID
    const app = getApp();
    const userId = app.globalData.userId || wx.getStorageSync('userId');
    
    // 准备基本表单数据（不包含位置信息）
    const baseFormData = {
      title: this.data.formData.title,                  // 植物名称
      height: this.data.formData.height,                // 高度
      canopy: this.data.formData.canopy,                // 冠幅
      meter_diameter: this.data.formData.meterDiameter, // 米经
      ground_diameter: this.data.formData.groundDiameter, // 地径
      cup: this.data.formData.cup,                      // 杯口
      branchPos: this.data.formData.branchPoint,        // 分支点
      quantity: this.data.formData.quantity,            // 需求数量
      unit: this.data.formData.unit || '棵',            // 单位，默认为"棵"
      quality: this.data.formData.quality,              // 求购质量
      buyArea: this.data.areaText || '', // 求购范围，直接使用字符串
      usingAddress: this.data.formData.purchaseLocation ? [this.data.formData.purchaseLocation] : [], // 用苗地点
      content: this.data.formData.content,              // 详细描述
      contactName: this.data.formData.contact,      // 联系人
      phoneNumber: this.data.formData.contactPhone,     // 联系电话
      uid: userId,                                      // 发布者ID
      createTime: new Date(),
      outDate: this.data.formData.outDate || '',        // 求购期限
      replyCount: 0,                                    // 回价数量，默认为0
      // 添加用户信息
      nickname: app.globalData.userInfo?.nickName || '匿名用户',
      avatarUrl: app.globalData.userInfo?.avatarUrl || '/images/avatar_placeholder.png',
      // 价格信息 - 允许为空
      price: this.data.formData.price !== '' && this.data.formData.price !== null && this.data.formData.price !== undefined ? parseFloat(this.data.formData.price) : null,
      // 添加价格状态标志
      price_status: this.data.formData.price !== '' && this.data.formData.price !== null && this.data.formData.price !== undefined ? '已设置' : '电话议价',
      // 添加额外规格参数
      main_vine_length: this.data.specAdded.mainVineLength ? this.data.formData.main_vine_length : '',
      plant_density: this.data.specAdded.plantDensity ? this.data.formData.plant_density : '',
      trunkDiameter: this.data.specAdded.trunkDiameter ? this.data.formData.trunkDiameter : '',
      thorax_diameter: this.data.specAdded.thoraxDiameter ? this.data.formData.thorax_diameter : '',
      branch_count: this.data.specAdded.branchCount ? this.data.formData.branch_count : '',
      plant_age: this.data.specAdded.plantAge ? this.data.formData.plant_age : '',
    };
    
    // 获取位置信息
    wx.getLocation({
      type: 'gcj02',
      isHighAccuracy: true,
      highAccuracyExpireTime: 3000,
      success: (res) => {
        const { latitude, longitude } = res;
        
        // 添加位置信息
        const formData = {
          ...baseFormData,
          location: {
            type: 'Point',
            coordinates: [longitude, latitude]
          }
        };
        
        this.processFormSubmission(formData, userId);
      },
      fail: () => {
        // 获取位置失败，使用不带位置信息的数据
        this.processFormSubmission(baseFormData, userId);
      }
    });
  },
  
  /**
   * 处理表单提交（上传图片和保存数据）
   */
  processFormSubmission(formData, userId) {
    // 修改逻辑：先保存数据到数据库获取_id，再上传图片
    // 保存临时图片路径
    const tempImageList = this.data.imageList;
    
    // 先将imageList设为空数组，后续上传成功后再更新
    formData.imageList = [];
    
    // 先保存到数据库获取帖子ID
    const db = wx.cloud.database();
    db.collection('demand_content').add({
      data: formData
    }).then(res => {
      // 获取新创建的帖子ID
      const postId = res._id;
      // console.log('创建帖子成功，ID:', postId); // 添加日志，便于调试

      // 创建通知记录
      this.createNoticeRecord(postId, formData.title, userId);

      // 如果有图片，上传图片并更新帖子数据
      if (tempImageList && tempImageList.length > 0) {
        // console.log(`准备上传 ${tempImageList.length} 张图片`); // 添加日志，便于调试
        
        this.uploadImages(userId, postId, tempImageList).then(fileIDs => {
          // console.log('所有图片上传成功:', fileIDs); // 添加日志，便于调试

          // 更新帖子，添加图片链接
          return db.collection('demand_content').doc(postId).update({
            data: {
              imageList: fileIDs
            }
          });
        }).then(() => {
          // 上传并更新成功
          // console.log('更新帖子图片列表成功'); // 添加日志，便于调试
          wx.hideLoading();
          wx.showToast({
            title: '发布成功',
            icon: 'success',
            duration: 2000,
            success: () => {
              this.handlePublishSuccess();
            }
          });
        }).catch(err => {
          console.error('上传或更新图片失败:', err);
          // 即使图片上传失败，帖子也已经创建成功，所以显示部分成功的消息
          wx.hideLoading();
          wx.showToast({
            title: '发布成功，但图片上传失败',
            icon: 'none',
            duration: 2000,
            success: () => {
              this.handlePublishSuccess();
            }
          });
        });
      } else {
        // 无图片，直接显示成功
        wx.hideLoading();
        wx.showToast({
          title: '发布成功',
          icon: 'success',
          duration: 2000,
          success: () => {
            this.handlePublishSuccess();
          }
        });
      }
    }).catch(err => {
      console.error('保存数据失败:', err);
      wx.hideLoading();
      wx.showToast({
        title: '发布失败，请重试',
        icon: 'none'
      });
      this.setData({ submitting: false });
    });
  },

  /**
   * 创建通知记录
   * @param {String} postId 帖子ID
   * @param {String} plantName 植物名称
   * @param {String} publisherId 发布者ID
   */
  createNoticeRecord(postId, plantName, publisherId) {
    // 获取数据库引用
    const db = wx.cloud.database();
    
    // console.log('开始创建通知记录:', {
    //   postId: postId,
    //   plantName: plantName,
    //   publisherId: publisherId
    // });
    
    // 获取用户信息
    const app = getApp();
    const userInfo = app.globalData.userInfo || wx.getStorageSync('userInfo');
    
    // 获取用户昵称，如果没有则使用默认值
    const publisherName = userInfo && userInfo.nickName ? userInfo.nickName : '用户';
    
    // 构建通知文本
    const noticeText = `${publisherName} 发布了一条 ${plantName} 求购信息`;
    
    // 创建通知数据 - 确保字段格式与集合一致
    const noticeData = {
      postId: postId,             // 关联帖子ID
      postType: 'demand',         // 帖子类型: 'demand'
      publisherId: publisherId,   // 发布者ID
      publisherName: publisherName, // 发布者昵称
      plantName: plantName,       // 植物名称
      noticeText: noticeText,     // 通知文本
      createTime: db.serverDate(), // 创建时间，使用服务器时间
      isPublic: true,             // 是否公开展示
      isRead: false,              // 是否已读
      isDeleted: false            // 是否已删除
    };
    
    // 在控制台打印将要创建的通知数据
    // console.log('通知数据:', noticeData);

    // 直接向notice集合添加记录
    db.collection('notice').add({
      data: noticeData
    }).then(res => {
      // console.log('创建通知成功，ID:', res._id);
    }).catch(err => {
      console.error('创建通知失败，错误码:', err.errCode, '错误信息:', err.errMsg);
      
      // 根据错误代码提供更具体的信息
      if (err.errCode === -502005) {
        console.error('notice集合不存在，请在云开发控制台创建该集合');
      } else if (err.errCode === -502001) {
        console.error('notice集合权限不足，请检查集合权限设置');
      } else if (err.errCode === -502002) {
        console.error('notice集合字段验证失败，请检查数据格式');
      }
    });
  },

  /**
   * 处理发布成功后的操作
   */
  handlePublishSuccess() {
    // 更新发布信息统计数据
    const db = wx.cloud.database();
    db.collection('appData').doc('61493796683c0eae01a1f4045d9b0e0b').update({
      data: {
        totalPost: db.command.inc(1)
      }
    }).catch(err => {
      console.error('更新发布统计数据失败:', err);
    });
    
    // 发布成功后显示继续发布弹窗
    setTimeout(() => {
      // 设置需要刷新标志
      const pages = getCurrentPages();
      if (pages.length > 1) {
        const prevPage = pages[pages.length - 2];
        if (prevPage.route.includes('demand')) {
          prevPage.setData({
            needRefresh: true
          });
        }
      }
      this.setData({ showContinueModal: true });
    }, 2000);

    // 🛡️ 发布成功后5秒安全时间，再重置提交状态
    setTimeout(() => {
      this.setData({ submitting: false });
    }, 5000);
  },

  /**
   * 上传图片
   * 按照要求的命名规则上传图片
   */
  uploadImages(userId, postId, imageList) {
    // 使用真实的userId和postId来命名图片目录
    const uploadTasks = imageList.map((filePath, index) => {
      // 获取文件扩展名
      const fileExt = filePath.match(/\.(\w+)$/)[1] || 'jpg';
      
      // 按照要求的命名规则：demand_post/userId_postId/图片索引.扩展名
      // 注意：文件夹名称是 userId_postId，确保与需求一致
      const cloudPath = `demand_post/${userId}_${postId}/image_${index}.${fileExt}`;
      
      // console.log('上传图片路径:', cloudPath); // 添加日志，便于调试

      return wx.cloud.uploadFile({
        cloudPath,
        filePath
      }).then(res => {
        // console.log('图片上传成功:', res.fileID); // 添加日志，便于调试
        return res.fileID;
      }).catch(err => {
        console.error('图片上传失败:', err);
        throw err;
      });
    });
    
    return Promise.all(uploadTasks);
  },

  /**
   * 取消发布
   */
  cancelPublish() {
    wx.navigateBack();
  },

  /**
   * 选择地理位置
   */
  chooseLocation() {
    wx.chooseLocation({
      success: (res) => {
        // 成功选择地点后的回调
        if (res.address) {
          this.setData({
            'formData.purchaseLocation': res.address,
            'errors.purchaseLocation': ''
          });
        }
      },
      fail: (err) => {
        console.error('选择地点失败:', err);
        // 如果是因为用户拒绝授权导致的失败，引导用户开启授权
        if (err.errMsg.indexOf('auth deny') >= 0) {
          wx.showModal({
            title: '提示',
            content: '需要您授权使用位置信息',
            confirmText: '去授权',
            success: (res) => {
              if (res.confirm) {
                wx.openSetting({
                  success: (settingRes) => {
                    if (settingRes.authSetting['scope.userLocation']) {
                      // 用户已授权，可以重新调用选择位置
                      this.chooseLocation();
                    }
                  }
                });
              }
            }
          });
        }
      }
    });
  },

  /**
   * 处理植物名输入框点击事件
   */
  onTitleClick() {
    // 如果输入框已经获得焦点，则不做任何操作
    if (this.data.titleInputFocused) {
      return;
    }

    // 获取当前窗口信息
    const windowInfo = wx.getWindowInfo();
    const windowHeight = windowInfo.windowHeight;
    
    // 滚动屏幕高度的三分之一，为搜索提示留出空间
    const scrollToPosition = windowHeight /4 ;
    
    // 滚动到指定位置
    wx.pageScrollTo({
      scrollTop: scrollToPosition,
      duration: 300
    });
  },

  /**
   * 处理输入框获得焦点
   */
  onTitleFocus() {
    // 标记输入框已获得焦点
    this.setData({
      titleInputFocused: true
    });
    
    // 调用搜索工具处理焦点事件
    searchUtils.handleTitleFocus(this);
    
    // 获取当前窗口信息
    const windowInfo = wx.getWindowInfo();
    const windowHeight = windowInfo.windowHeight;
    
    // 滚动屏幕高度的三分之一，为搜索提示留出空间
    const scrollToPosition = windowHeight / 4;
    
    // 滚动到指定位置
    wx.pageScrollTo({
      scrollTop: scrollToPosition,
      duration: 300
    });
  },

  /**
   * 处理输入框失去焦点
   */
  onTitleBlur() {
    // 标记输入框已失去焦点
    this.setData({
      titleInputFocused: false
    });
    
    // 调用搜索工具处理失焦事件
    searchUtils.handleTitleBlur(this);
  },

  /**
   * 选择推荐的植物名
   */
  onSelectSuggestion(e) {
    searchUtils.handleSelectSuggestion(this, e, false); // 不自动填充品种
  },

  /**
   * 关闭植物名称推荐列表
   */
  closeSuggestions() {
    searchUtils.closeSuggestions(this);
  },



  /**
   * Radio选择求购期限
   */
  onOutDateRadioChange(e) {
    const value = e.detail.value;
    this.setData({
      'formData.outDate': value,
      'errors.outDate': ''
    });
  },

  /**
   * Radio选择求购质量
   */
  onQualityRadioChange(e) {
    const value = e.detail.value;
    this.setData({
      'formData.quality': value,
      'errors.quality': ''
    });
  },

  // 阻止事件冒泡
  stopPropagation(e) {
    searchUtils.stopPropagation(e);
  },

  /**
   * 切换额外规格参数的显示状态
   */
  toggleExtraSpecs: function() {
    this.setData({
      showExtraSpecs: !this.data.showExtraSpecs
    });
  },

  /**
   * 添加规格参数
   */
  addSpec: function(e) {
    const spec = e.currentTarget.dataset.spec;
    const specAdded = {...this.data.specAdded};
    
    // 检查参数是否已有值
    const fieldMap = {
      mainVineLength: 'main_vine_length',
      plantDensity: 'plant_density',
      trunkDiameter: 'trunkDiameter',
      thoraxDiameter: 'thorax_diameter',
      branchCount: 'branch_count',
      plantAge: 'plant_age',
    };
    
    const fieldName = fieldMap[spec];
    if (!this.data.formData[fieldName]) {
      wx.showToast({
        title: '请先填写参数值',
        icon: 'none'
      });
      return;
    }
    
    specAdded[spec] = true;
    
    this.setData({
      specAdded: specAdded,
      hasAddedSpecs: true,
      showExtraSpecs: false // 添加后关闭额外参数面板
    });
    
    // 延迟一点再显示已添加的参数
    setTimeout(() => {
      this.setData({
        showExtraSpecs: false
      });
    }, 300);
  },

  /**
   * 移除规格参数
   */
  removeSpec: function(e) {
    const spec = e.currentTarget.dataset.spec;
    const specAdded = {...this.data.specAdded};
    
    specAdded[spec] = false;
    
    // 检查是否还有其他已添加的参数
    const hasAnySpec = Object.values(specAdded).some(value => value);
    
    this.setData({
      specAdded: specAdded,
      hasAddedSpecs: hasAnySpec
    });
  },

  /**
   * 隐藏继续发布弹窗
   */
  hideContinueModal() {
    this.setData({ showContinueModal: false });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  },

  /**
   * 取消继续发布，返回首页
   */
  cancelContinue() {
    this.setData({ showContinueModal: false });
    wx.switchTab({
      url: '/pages/demand/demand'
    });
  },

  /**
   * 确认继续发布
   */
  confirmContinue() {
    this.setData({ showContinueModal: false });

    // 保存当前的联系电话和位置信息
    const currentPhone = this.data.formData.contactPhone;
    const currentContact = this.data.formData.contact;
    const currentAreaText = this.data.areaText;
    const currentAreaValue = this.data.areaValue;
    const currentPurchaseArea = this.data.formData.purchaseArea;

    // 重新打开发布页面（重置表单但保留电话和位置）
    wx.redirectTo({
      url: `/pages/demand/publish/publish?phone=${currentPhone}&contact=${encodeURIComponent(currentContact)}&areaText=${encodeURIComponent(currentAreaText)}&areaValue=${JSON.stringify(currentAreaValue)}&purchaseArea=${encodeURIComponent(currentPurchaseArea)}`
    });
  }
});