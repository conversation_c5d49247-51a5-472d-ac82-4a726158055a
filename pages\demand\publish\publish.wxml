<!-- pages/demand/publish/publish.wxml -->
<page-meta>
  <navigation-bar title="发布求购" back="{{true}}" homeButton="{{true}}" extClass="custom-nav" hideTree="{{true}}" bindback="onNavBack"></navigation-bar>
</page-meta>
<view class="container">
  <view class="form-container">
  
    <!-- 图片上传 - 移到最上方 -->
    <view class="form-section card-shadow image-section">
      <view class="image-uploader {{errors.imageList ? 'error' : ''}}">
        <view class="image-list">
          <block wx:for="{{imageList}}" wx:key="index">
            <view class="image-item">
              <image src="{{item}}" mode="aspectFill" bindtap="previewImage" data-index="{{index}}"></image>
              <view class="delete-btn-wrapper" hover-class="delete-btn-wrapper-hover" catchtap="deleteImage" data-index="{{index}}">
                <view class="delete-btn">×</view>
              </view>
            </view>
          </block>

          <view class="upload-btn camera-btn" hover-class="upload-btn-hover" bindtap="chooseImage" wx:if="{{imageList.length < 6}}">
            <view class="upload-icon">
              <t-icon name="camera-filled" size="42rpx" color="#07c160"></t-icon>
            </view>
            <view class="upload-text">拍照上传</view>
          </view>
        </view>
        <view class="image-tips">最多上传3张样本图</view>
      </view>
      <view wx:if="{{errors.imageList}}" class="error-msg">{{errors.imageList}}</view>
    </view>

    <!-- 苗木规格 -->
    <view class="form-section card-shadow">
      <!-- 苗木规格设置 -->
      <view class="section-title">苗木规格<text class="highlight-text">（至少填写一个参数）</text>
        <!-- 切换按钮 -->
        <view class="toggle-button" hover-class="toggle-button-hover" bindtap="switchToSupply">
          <view class="toggle-icon">↔</view>
          <view class="toggle-text">切换到供应</view>
        </view>
      </view>

      <!-- 第一行：植物名独占一行，但宽度调短 -->
      <view class="spec-row">
        <view class="spec-item required-spec" id="title-input-section" bindtap="onTitleClick" style="margin-top: 4%; width: 89%;">
          <view class="form-label">求购名<text class="required">*</text></view>
          <view class="measure-input-container {{errors.title ? 'error' : ''}}">
            <input class="form-input" type="text" placeholder="请输入植物名称" placeholder-class="spec-placeholder" value="{{formData.title}}" bindinput="onInputChange" data-field="title" bindfocus="onTitleFocus" bindblur="onTitleBlur" adjust-position="{{false}}" />
          </view>
          <view wx:if="{{errors.title}}" class="error-msg">{{errors.title}}</view>

          <!-- 植物名称推荐列表 -->
          <view class="plant-suggestions-container" wx:if="{{showSuggestions && plantSuggestions.length > 0}}" catchtap="stopPropagation" catchtouchmove="stopPropagation" catchlongpress="stopPropagation">
            <view class="suggestions-header">
              <text class="suggestions-title">你想输入 (共{{plantSuggestions.length}}项)</text>
              <view class="suggestions-close" bindtap="closeSuggestions">×</view>
            </view>
            <scroll-view scroll-y class="suggestions-scroll" catchtouchmove="stopPropagation">
              <view class="suggestion-item" wx:for="{{plantSuggestions}}" wx:key="index" hover-class="suggestion-item-hover" catchtap="onSelectSuggestion" data-name="{{item.name}}">
                    <view class="suggestion-name">{{item.name}}</view>
              </view>
            </scroll-view>
          </view>
        </view>
      </view>

      <!-- 第二行：米径、地径 -->
      <view class="spec-row">
        <view class="spec-item required-spec" style="width: 38%;">
          <view class="form-label">米径</view>
          <view class="measure-input-container {{errors.meterDiameter ? 'error' : ''}}">
            <input class="form-input" type="digit" placeholder="1米处量" placeholder-class="spec-placeholder" value="{{formData.meterDiameter}}" bindinput="onInputChange" data-field="meterDiameter" />
          </view>
          <view class="unit-text">公分</view>
          <view wx:if="{{errors.meterDiameter}}" class="error-msg">{{errors.meterDiameter}}</view>
        </view>

        <view class="spec-item" style="width: 40%;">
          <view class="form-label">地径</view>
          <view class="measure-input-container {{errors.groundDiameter ? 'error' : ''}}">
            <input class="form-input" type="digit" placeholder="20公分量" placeholder-class="spec-placeholder" value="{{formData.groundDiameter}}" bindinput="onInputChange" data-field="groundDiameter" />
          </view>
          <view class="unit-text">公分</view>
          <view wx:if="{{errors.groundDiameter}}" class="error-msg">{{errors.groundDiameter}}</view>
        </view>
      </view>

      <!-- 第三行：高度、冠幅 -->
      <view class="spec-row">
        <view class="spec-item required-spec" style="width: 38%;">
          <view class="form-label">高度</view>
          <view class="measure-input-container {{errors.height ? 'error' : ''}}">
            <input class="form-input" type="digit" placeholder="输入高度" placeholder-class="spec-placeholder" value="{{formData.height}}" bindinput="onInputChange" data-field="height" />
          </view>
          <view class="unit-text">公分</view>
          <view wx:if="{{errors.height}}" class="error-msg">{{errors.height}}</view>
        </view>

        <view class="spec-item" style="width: 40%;">
          <view class="form-label">冠幅</view>
          <view class="measure-input-container {{errors.canopy ? 'error' : ''}}">
            <input class="form-input" type="digit" placeholder="输入冠幅" placeholder-class="spec-placeholder" value="{{formData.canopy}}" bindinput="onInputChange" data-field="canopy" />
          </view>
          <view class="unit-text">公分</view>
          <view wx:if="{{errors.canopy}}" class="error-msg">{{errors.canopy}}</view>
        </view>
      </view>

      <!-- 第四行：杯口、丛生 -->
      <view class="spec-row">
        <view class="spec-item" style="width: 38%;">
          <view class="form-label">杯口</view>
          <view class="measure-input-container {{errors.cup ? 'error' : ''}}">
            <input class="form-input" type="digit" placeholder="输入杯口" placeholder-class="spec-placeholder" value="{{formData.cup}}" bindinput="onInputChange" data-field="cup" />
          </view>
          <view class="unit-text">杯</view>
          <view wx:if="{{errors.cup}}" class="error-msg">{{errors.cup}}</view>
        </view>

        <view class="spec-item" style="width: 40%;">
          <view class="form-label">丛生</view>
          <view class="measure-input-container {{errors.branchPoint ? 'error' : ''}}">
            <input class="form-input" type="digit" placeholder="分枝处" placeholder-class="spec-placeholder" value="{{formData.branchPoint}}" bindinput="onInputChange" data-field="branchPoint" />
          </view>
          <view class="unit-text">公分</view>
          <view wx:if="{{errors.branchPoint}}" class="error-msg">{{errors.branchPoint}}</view>
        </view>
      </view>

      <!-- 增加参数按钮 -->
      <!-- <view class="add-spec-btn" catchtap="toggleExtraSpecs">
        <view class="add-icon">+</view>
        <view class="add-text">增加参数</view>
      </view> -->

      <!-- 额外的规格参数 -->

      
      <!-- <view class="extra-specs" wx:if="{{showExtraSpecs}}">
        <view class="specs-grid"> -->
          <!-- 主蔓长度 -->
          <!-- <view class="spec-item" wx:if="{{!specAdded.mainVineLength}}">
            <view class="form-label">主蔓长度</view>
            <view class="measure-input-container {{errors.main_vine_length ? 'error' : ''}}">
              <input class="form-input" type="digit" placeholder="输入长度" placeholder-class="spec-placeholder" value="{{formData.main_vine_length}}" bindinput="onInputChange" data-field="main_vine_length" />
              <view class="unit-text">公分</view>
            </view>
            <view class="add-this-spec" catchtap="addSpec" data-spec="mainVineLength">添加</view>
            <view wx:if="{{errors.main_vine_length}}" class="error-msg">{{errors.main_vine_length}}</view>
          </view> -->

          <!-- 密度(株/m²) -->
          <!-- <view class="spec-item" wx:if="{{!specAdded.plantDensity}}">
            <view class="form-label">密度</view>
            <view class="measure-input-container {{errors.plant_density ? 'error' : ''}}">
              <input class="form-input" type="digit" placeholder="输入密度" placeholder-class="spec-placeholder" value="{{formData.plant_density}}" bindinput="onInputChange" data-field="plant_density" />
              <view class="unit-text">株/m²</view>
            </view>
            <view class="add-this-spec" catchtap="addSpec" data-spec="plantDensity">添加</view>
            <view wx:if="{{errors.plant_density}}" class="error-msg">{{errors.plant_density}}</view>
          </view> -->

          <!-- 杆径 -->
          <!-- <view class="spec-item" wx:if="{{!specAdded.trunkDiameter}}">
            <view class="form-label">杆径</view>
            <view class="measure-input-container {{errors.trunkDiameter ? 'error' : ''}}">
              <input class="form-input" type="digit" placeholder="输入杆径" placeholder-class="spec-placeholder" value="{{formData.trunkDiameter}}" bindinput="onInputChange" data-field="trunkDiameter" />
              <view class="unit-text">公分</view>
            </view>
            <view class="add-this-spec" catchtap="addSpec" data-spec="trunkDiameter">添加</view>
            <view wx:if="{{errors.trunkDiameter}}" class="error-msg">{{errors.trunkDiameter}}</view>
          </view> -->

          <!-- 胸径 -->
          <!-- <view class="spec-item" wx:if="{{!specAdded.thoraxDiameter}}">
            <view class="form-label">胸径</view>
            <view class="measure-input-container {{errors.thorax_diameter ? 'error' : ''}}">
              <input class="form-input" type="digit" placeholder="输入胸径" placeholder-class="spec-placeholder" value="{{formData.thorax_diameter}}" bindinput="onInputChange" data-field="thorax_diameter" />
              <view class="unit-text">公分</view>
            </view>
            <view class="add-this-spec" catchtap="addSpec" data-spec="thoraxDiameter">添加</view>
            <view wx:if="{{errors.thorax_diameter}}" class="error-msg">{{errors.thorax_diameter}}</view>
          </view> -->

          <!-- 分支数 -->
          <!-- <view class="spec-item" wx:if="{{!specAdded.branchCount}}">
            <view class="form-label">分支数</view>
            <view class="measure-input-container {{errors.branch_count ? 'error' : ''}}">
              <input class="form-input" type="digit" placeholder="输入分支数" placeholder-class="spec-placeholder" value="{{formData.branch_count}}" bindinput="onInputChange" data-field="branch_count" />
              <view class="unit-text">支</view>
            </view>
            <view class="add-this-spec" catchtap="addSpec" data-spec="branchCount">添加</view>
            <view wx:if="{{errors.branch_count}}" class="error-msg">{{errors.branch_count}}</view>
          </view> -->

          <!-- 苗龄 -->
          <!-- <view class="spec-item" wx:if="{{!specAdded.plantAge}}">
            <view class="form-label">苗龄</view>
            <view class="measure-input-container {{errors.plant_age ? 'error' : ''}}">
              <input class="form-input" type="digit" placeholder="输入苗龄" placeholder-class="spec-placeholder" value="{{formData.plant_age}}" bindinput="onInputChange" data-field="plant_age" />
             
            </view>
            <view class="add-this-spec" catchtap="addSpec" data-spec="plantAge">添加</view>
            <view wx:if="{{errors.plant_age}}" class="error-msg">{{errors.plant_age}}</view>
          </view> -->

          <!-- 栽培状态 -->
          <!-- <view class="form-item" wx:if="{{specAdded.plantMethod}}">
            <view class="form-label">栽培状态</view>
            <picker mode="selector" range="{{plantMethodOptions}}" bindchange="onPlantMethodChange">
              <view class="picker {{errors.plant_method ? 'error' : ''}}">
                <text>{{formData.plant_method || '请选择'}}</text>
                <text class="arrow">></text>
              </view>
            </picker>
            <view wx:if="{{errors.plant_method}}" class="error-msg">{{errors.plant_method}}</view>
          </view>
        </view>
      </view> -->

      <!-- 已添加的额外参数 -->
      <!-- <view class="added-specs" wx:if="{{hasAddedSpecs}}">
        <view class="sub-section-title">已添加的额外参数</view>
        <view class="specs-grid"> -->
          <!-- 主蔓长度 -->
          <!-- <view class="spec-item" wx:if="{{specAdded.mainVineLength}}">
            <view class="form-label">主蔓长度</view>
            <view class="measure-input-container {{errors.main_vine_length ? 'error' : ''}}">
              <input class="form-input" type="digit" placeholder="输入长度" placeholder-class="spec-placeholder" value="{{formData.main_vine_length}}" bindinput="onInputChange" data-field="main_vine_length" />
              <view class="unit-text">公分</view>
            </view>
            <view class="remove-spec" catchtap="removeSpec" data-spec="mainVineLength">×</view>
            <view wx:if="{{errors.main_vine_length}}" class="error-msg">{{errors.main_vine_length}}</view>
          </view> -->

          <!-- 密度(株/m²) -->
          <!-- <view class="spec-item" wx:if="{{specAdded.plantDensity}}">
            <view class="form-label">密度</view>
            <view class="measure-input-container {{errors.plant_density ? 'error' : ''}}">
              <input class="form-input" type="digit" placeholder="输入密度" placeholder-class="spec-placeholder" value="{{formData.plant_density}}" bindinput="onInputChange" data-field="plant_density" />
              <view class="unit-text">株/m²</view>
            </view>
            <view class="remove-spec" catchtap="removeSpec" data-spec="plantDensity">×</view>
            <view wx:if="{{errors.plant_density}}" class="error-msg">{{errors.plant_density}}</view>
          </view> -->

          <!-- 杆径 -->
          <!-- <view class="spec-item" wx:if="{{specAdded.trunkDiameter}}">
            <view class="form-label">杆径</view>
            <view class="measure-input-container {{errors.trunkDiameter ? 'error' : ''}}">
              <input class="form-input" type="digit" placeholder="输入杆径" placeholder-class="spec-placeholder" value="{{formData.trunkDiameter}}" bindinput="onInputChange" data-field="trunkDiameter" />
              <view class="unit-text">公分</view>
            </view>
            <view class="remove-spec" catchtap="removeSpec" data-spec="trunkDiameter">×</view>
            <view wx:if="{{errors.trunkDiameter}}" class="error-msg">{{errors.trunkDiameter}}</view>
          </view> -->

          <!-- 胸径 -->
          <!-- <view class="spec-item" wx:if="{{specAdded.thoraxDiameter}}">
            <view class="form-label">胸径</view>
            <view class="measure-input-container {{errors.thorax_diameter ? 'error' : ''}}">
              <input class="form-input" type="digit" placeholder="输入胸径" placeholder-class="spec-placeholder" value="{{formData.thorax_diameter}}" bindinput="onInputChange" data-field="thorax_diameter" />
              <view class="unit-text">公分</view>
            </view>
            <view class="remove-spec" catchtap="removeSpec" data-spec="thoraxDiameter">×</view>
            <view wx:if="{{errors.thorax_diameter}}" class="error-msg">{{errors.thorax_diameter}}</view>
          </view> -->

          <!-- 分支数 -->
          <!-- <view class="spec-item" wx:if="{{specAdded.branchCount}}">
            <view class="form-label">分支数</view>
            <view class="measure-input-container {{errors.branch_count ? 'error' : ''}}">
              <input class="form-input" type="digit" placeholder="输入分支数" placeholder-class="spec-placeholder" value="{{formData.branch_count}}" bindinput="onInputChange" data-field="branch_count" />
              <view class="unit-text">支</view>
            </view>
            <view class="remove-spec" catchtap="removeSpec" data-spec="branchCount">×</view>
            <view wx:if="{{errors.branch_count}}" class="error-msg">{{errors.branch_count}}</view>
          </view> -->

          <!-- 苗龄 -->
          <!-- <view class="spec-item" wx:if="{{specAdded.plantAge}}">
            <view class="form-label">苗龄</view>
            <view class="measure-input-container {{errors.plant_age ? 'error' : ''}}">
              <input class="form-input" type="digit" placeholder="输入苗龄" placeholder-class="spec-placeholder" value="{{formData.plant_age}}" bindinput="onInputChange" data-field="plant_age" />
              <view class="unit-text">年</view>
            </view>
            <view class="remove-spec" catchtap="removeSpec" data-spec="plantAge">×</view>
            <view wx:if="{{errors.plant_age}}" class="error-msg">{{errors.plant_age}}</view>
          </view> -->

          <!-- 栽培状态 -->
          <!-- <view class="form-item" wx:if="{{!specAdded.plantMethod}}">
            <view class="form-label">栽培状态</view>
            <picker mode="selector" range="{{plantMethodOptions}}" bindchange="onPlantMethodChange">
              <view class="picker {{errors.plant_method ? 'error' : ''}}">
                <text>{{formData.plant_method || '请选择'}}</text>
                <text class="arrow">></text>
              </view>
            </picker>
            <view wx:if="{{errors.plant_method}}" class="error-msg">{{errors.plant_method}}</view>
          </view>
        </view>
      </view> -->
    </view>

    <!-- 基本信息 -->
    <view class="form-section card-shadow">
      <!-- 标题 -->
      <view class="section-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20rpx;">
        <view class="section-title">基本信息</view>
      </view>
      
      <!-- 两列式flex流式布局 -->
      <view style="display: flex; flex-wrap: wrap; width: 100%;">
        <!-- 第一行：价格设置独占一行 -->
        <view style="display: flex; width: 100%; margin-bottom: 15rpx;">
          <!-- 价格设置 -->
          <view style="display: flex; width: 50%; box-sizing: border-box;">
            <view class="form-label" style="min-width: 120rpx; background-color: rgba(255, 152, 0, 0.05); border-radius: 6rpx; color: #ff9800; padding: 2rpx 8rpx; border-left: 3rpx solid #ff9800;">价格设置</view>
            <view class="price-input-container {{errors.price ? 'error' : ''}}" style="flex: 1; padding: 0 5rpx;">
              <input class="form-input price-input" style="padding: 6 10rpx;" type="digit" placeholder="可不填" placeholder-style="font-size: 20rpx;" value="{{formData.price}}" bindinput="onInputChange" data-field="price" />
              <view class="unit-text">元</view>
            </view>
          </view>
        </view>

        <!-- 第二行：数量独占一行 -->
        <view style="display: flex; width: 100%; margin-bottom: 15rpx;">
          <!-- 数量和单位 -->
          <view style="display: flex; width: 50%; box-sizing: border-box;">
            <view class="form-label" style="min-width: 120rpx; background-color: rgba(255, 152, 0, 0.05); border-radius: 6rpx; color: #ff9800; padding: 2rpx 8rpx; border-left: 3rpx solid #ff9800;">数量<text class="required">*</text></view>
            <view class="quantity-input-container {{errors.quantity ? 'error' : ''}}" style="flex: 1; display: flex; align-items: center;">
              <input class="form-input" type="digit" placeholder="" value="{{formData.quantity}}" bindinput="onInputChange" data-field="quantity" style="flex: 1;" />
              <picker mode="selector" range="{{unitOptions}}" bindchange="onUnitChange" class="unit-picker" style="margin-left: 5rpx; height: 80%;">
                <view class="unit-picker-text">
                  <text>{{formData.unit}}</text>
                  <view class="picker-arrow-small"></view>
                </view>
              </picker>
            </view>
          </view>
        </view>

        <!-- 第三行：期限选择（独占一行） -->
        <view style="width: 100%; margin-bottom: 20rpx; display: flex; align-items: center;">
          <view class="form-label" style="margin-right: 15rpx; background-color: rgba(255, 152, 0, 0.05); border-radius: 6rpx; color: #ff9800; padding: 6rpx 10rpx; border-left: 3rpx solid #ff9800; min-width: 120rpx; flex-shrink: 0;">求购期限</view>
          <radio-group bindchange="onOutDateRadioChange" class="radio-group" style="flex: 1;">
            <view class="radio-options">
              <label class="radio-option" wx:for="{{outDateRadioOptions}}" wx:key="value">
                <radio value="{{item.value}}" checked="{{formData.outDate === item.value}}"/>
                <text class="radio-text">{{item.label}}</text>
              </label>
            </view>
          </radio-group>
          <view wx:if="{{errors.outDate}}" class="error-msg">{{errors.outDate}}</view>
        </view>

        <!-- 第四行：求购质量选择（独占一行） -->
        <view style="width: 100%; margin-bottom: 20rpx; display: flex; align-items: center;">
          <view class="form-label" style="margin-right: 20rpx; background-color: rgba(255, 152, 0, 0.05); border-radius: 6rpx; color: #ff9800; padding: 8rpx 12rpx; border-left: 3rpx solid #ff9800; min-width: 80rpx;">求购质量<text class="required">*</text></view>
          <radio-group bindchange="onQualityRadioChange" class="radio-group" style="flex: 1;">
            <view class="radio-options">
              <label class="radio-option" wx:for="{{qualityRadioOptions}}" wx:key="value">
                <radio value="{{item.value}}" checked="{{formData.quality === item.value}}" />
                <text class="radio-text">{{item.label}}</text>
              </label>
            </view>
          </radio-group>
          <view wx:if="{{errors.quality}}" class="error-msg">{{errors.quality}}</view>
        </view>

        <!-- 第五行：采购范围和用苗地点 -->
        <view style="display: flex; width: 100%; margin-bottom: 15rpx;">
          <!-- 采购范围 -->
          <view style="display: flex; width: 50%; padding-right: 10rpx; box-sizing: border-box;">
            <view class="form-label" style="min-width: 120rpx; font-weight: 600;">采购范围</view>
            <view class="area-picker-trigger {{errors.purchaseArea ? 'error' : ''}}" bindtap="onAreaPicker" style="flex: 1;">
              <text>{{areaText || '请选择采购范围'}}</text>
              <view class="picker-arrow"></view>
            </view>
          </view>

          <!-- 用苗地点 -->
          <view style="display: flex; width: 50%; padding-left: 10rpx; box-sizing: border-box;">
            <view class="form-label" style="min-width: 120rpx; font-weight: 600;">用苗地点</view>
            <view class="location-picker-trigger {{errors.purchaseLocation ? 'error' : ''}}" bindtap="chooseLocation" style="flex: 1; display: flex; justify-content: space-between; align-items: center; padding-right: 10rpx;">
              <view class="location-text-container" style="flex: 1; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                <text class="location-text" style="font-size: 24rpx; ">{{formData.purchaseLocation ? '已选择地点' : '选择苗地'}}</text>
              </view>
              <view class="location-icon" style="display: flex; align-items: center; justify-content: center; width: 48rpx; flex-shrink: 0;">
                <t-icon name="location" size="40rpx" color="#07c160"></t-icon>
              </view>
            </view>
          </view>
          
        </view>
        
        <!-- 选择的地理位置完整显示 -->
        <view wx:if="{{formData.purchaseLocation}}" class="full-location-display">
          <view class="location-label">已选择用苗地点：</view>
          <view class="location-value">{{formData.purchaseLocation}}</view>
        </view>
        
        <!-- 错误信息 -->
        <view style="display: flex; flex-wrap: wrap; width: 100%; margin-top: 5rpx;">
          <view wx:if="{{errors.price}}" class="error-msg" style="margin-right: 20rpx; position: static;">{{errors.price}}</view>
          <view wx:if="{{errors.quantity}}" class="error-msg" style="margin-right: 20rpx; position: static;">{{errors.quantity}}</view>
          <view wx:if="{{errors.unit}}" class="error-msg" style="margin-right: 20rpx; position: static;">{{errors.unit}}</view>
          <view wx:if="{{errors.outDate}}" class="error-msg" style="margin-right: 20rpx; position: static;">{{errors.outDate}}</view>
          <view wx:if="{{errors.quality}}" class="error-msg" style="margin-right: 20rpx; position: static;">{{errors.quality}}</view>
          <view wx:if="{{errors.purchaseArea}}" class="error-msg" style="margin-right: 20rpx; position: static;">{{errors.purchaseArea}}</view>
          <view wx:if="{{errors.purchaseLocation}}" class="error-msg" style="margin-right: 20rpx; position: static;">{{errors.purchaseLocation}}</view>
        </view>
      </view>
    </view>

    <!-- 补充信息 -->
    <view class="form-section card-shadow">
      <view class="section-header">
        <view class="section-title" style="position: relative; left: 0rpx; top: 14rpx">补充信息</view>
      </view>
      <textarea class="form-textarea {{errors.content ? 'error' : ''}}" placeholder="填写补充信息（可选）" value="{{formData.content}}" bindinput="onInputChange" data-field="content" maxlength="500"></textarea>
      <view class="textarea-counter">{{formData.content.length || 0}}/500</view>
      <view wx:if="{{errors.content}}" class="error-msg">{{errors.content}}</view>
    </view>

    <!-- 联系方式 -->
    <view class="form-section card-shadow">
      <view class="section-header">
        <view class="section-title">联系方式</view>
      </view>

      <!-- 联系电话 - 改为水平布局 -->
      <view class="form-item horizontal">
        <view class="form-label" style="font-weight: 600;">联系电话<text class="required">*</text></view>
        <input class="form-input {{errors.contactPhone ? 'error' : ''}}" type="number" placeholder="请输入联系电话" value="{{formData.contactPhone}}" bindinput="onInputChange" data-field="contactPhone" style="flex: 1;" />
      </view>
      <view wx:if="{{errors.contactPhone}}" class="error-msg" style="margin-left: 130rpx;">{{errors.contactPhone}}</view>
    </view>

    <!-- 底部按钮区域 -->
    <view class="footer-buttons">
      <button class="cancel-btn" bindtap="cancelPublish">取消</button>
      <button class="submit-btn" bindtap="submitForm" disabled="{{submitting}}" loading="{{submitting}}">发布求购</button>
    </view>
  </view>

  <!-- 继续发布提示弹窗 -->
  <view class="continue-publish-modal" wx:if="{{showContinueModal}}">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-title">发布成功！</view>
      <view class="modal-message">返回大厅查看你的求购信息</view>
      <view class="modal-buttons">
        <button class="modal-btn cancel-btn" bindtap="cancelContinue">返回大厅</button>
        <button class="modal-btn confirm-btn" bindtap="confirmContinue">继续发布</button>
      </view>
    </view>
  </view>
</view>

<!-- 地区选择器 -->
<t-picker visible="{{areaVisible}}" value="{{areaValue}}" title="选择地区" cancelBtn="取消" confirmBtn="确认" bindchange="onPickerChange" bindpick="onColumnChange" bindcancel="onPickerCancel">
  <t-picker-item options="{{provinces}}"></t-picker-item>
  <t-picker-item options="{{cities}}"></t-picker-item>
</t-picker>